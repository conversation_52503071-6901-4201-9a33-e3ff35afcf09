import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export interface MilestoneProgress {
  milestone_km: number;
  achieved: boolean;
  achieved_at: string | null;
  cashback_base: number;
}

export function useMilestoneData(date?: string) {
  const { user } = useAuth();

  const { data: milestoneProgress, isLoading: isLoadingMilestones } = useQuery({
    queryKey: ['milestone-progress', user?.id, date],
    queryFn: async () => {
      if (!user) return [];
      
      const targetDate = date || new Date().toISOString().split('T')[0];
      
      const { data, error } = await supabase.rpc('get_user_milestone_progress', {
        p_user_id: user.id,
        p_date: targetDate,
        p_max_milestones: 6
      });

      if (error) {
        console.error('Error fetching milestone progress:', error);
        return [];
      }
      
      return data as MilestoneProgress[];
    },
    enabled: !!user,
  });

  const { data: totalCashback, isLoading: isLoadingCashback } = useQuery({
    queryKey: ['milestone-cashback', user?.id, date],
    queryFn: async () => {
      if (!user) return 0;
      
      const targetDate = date || new Date().toISOString().split('T')[0];
      
      const { data, error } = await supabase.rpc('calculate_milestone_cashback', {
        p_user_id: user.id,
        p_start_date: targetDate,
        p_end_date: targetDate
      });

      if (error) {
        console.error('Error calculating milestone cashback:', error);
        return 0;
      }
      
      return data as number;
    },
    enabled: !!user,
  });

  return {
    milestoneProgress: milestoneProgress || [],
    totalCashback: totalCashback || 0,
    isLoadingMilestones,
    isLoadingCashback,
    isLoading: isLoadingMilestones || isLoadingCashback,
  };
}

// Helper function to calculate milestone completion from distance
export const calculateMilestonesFromDistance = (totalDistance: number, maxMilestones: number = 6) => {
  const milestones = [];
  for (let i = 1; i <= maxMilestones; i++) {
    const achieved = totalDistance >= i;
    const progress = achieved ? 100 : Math.min(Math.max((totalDistance - (i - 1)) * 100, 0), 100);
    
    milestones.push({
      milestone_km: i,
      achieved,
      achieved_at: achieved ? new Date().toISOString() : null,
      cashback_base: achieved ? getCashbackRate(i) : 0,
      progress
    });
  }
  return milestones;
};

// Helper function to get cashback rate for a specific kilometer
export const getCashbackRate = (km: number): number => {
  if (km >= 5) return 0.90;
  if (km >= 4) return 0.80;
  if (km >= 3) return 0.70;
  if (km >= 2) return 0.60;
  if (km >= 1) return 0.50;
  return 0;
};

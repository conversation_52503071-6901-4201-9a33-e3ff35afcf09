-- Verification script for milestone tracking setup
-- Run this in your Supabase SQL editor to verify everything is working

-- 1. Check if milestone table exists
SELECT 
    table_name, 
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'user_daily_milestones' 
ORDER BY ordinal_position;

-- 2. Check if milestone functions exist
SELECT 
    routine_name, 
    routine_type,
    routine_definition
FROM information_schema.routines 
WHERE routine_name IN (
    'get_user_milestone_progress',
    'calculate_milestone_cashback',
    'update_daily_milestones'
);

-- 3. Check if triggers exist
SELECT 
    trigger_name,
    event_manipulation,
    event_object_table,
    action_timing
FROM information_schema.triggers 
WHERE trigger_name = 'on_user_activity_update_milestones';

-- 4. Test milestone progress function (replace with actual user ID)
-- SELECT * FROM get_user_milestone_progress(
--     'your-user-id-here'::uuid,
--     CURRENT_DATE,
--     6
-- );

-- 5. Test cashback calculation function (replace with actual user ID)
-- SELECT calculate_milestone_cashback(
--     'your-user-id-here'::uuid,
--     CURRENT_DATE,
--     CURRENT_DATE
-- );

-- 6. Check existing cashback base function
SELECT get_cashback_base(1) as km1_cashback,
       get_cashback_base(2) as km2_cashback,
       get_cashback_base(3) as km3_cashback,
       get_cashback_base(4) as km4_cashback,
       get_cashback_base(5) as km5_cashback,
       get_cashback_base(6) as km6_cashback;

-- 7. Check RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'user_daily_milestones';

-- Expected Results:
-- 1. Should show 6 columns: id, user_id, activity_date, milestone_km, achieved_at, cashback_base, created_at
-- 2. Should show 3 functions: get_user_milestone_progress, calculate_milestone_cashback, update_daily_milestones
-- 3. Should show 1 trigger: on_user_activity_update_milestones
-- 4. Cashback values should be: 0.50, 0.60, 0.70, 0.80, 0.90, 0.90
-- 5. Should show 4 RLS policies for user access control

-- Migration to add milestone tracking support
-- This enhances the existing cashback system to support granular kilometer milestone tracking

-- Create a table to track individual milestone achievements per day
CREATE TABLE IF NOT EXISTS public.user_daily_milestones (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    activity_date DATE NOT NULL DEFAULT CURRENT_DATE,
    milestone_km INTEGER NOT NULL CHECK (milestone_km >= 1 AND milestone_km <= 10),
    achieved_at TIMESTAMPTZ,
    cashback_base NUMERIC(5, 2) NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    UNIQUE(user_id, activity_date, milestone_km)
);

-- Add comments for clarity
COMMENT ON TABLE public.user_daily_milestones IS 'Tracks individual kilometer milestone achievements per user per day';
COMMENT ON COLUMN public.user_daily_milestones.milestone_km IS 'The kilometer milestone (1, 2, 3, etc.)';
COMMENT ON COLUMN public.user_daily_milestones.achieved_at IS 'When the milestone was achieved (NULL if not achieved)';
COMMENT ON COLUMN public.user_daily_milestones.cashback_base IS 'Cashback base value for this milestone';

-- Enable Row-Level Security
ALTER TABLE public.user_daily_milestones ENABLE ROW LEVEL SECURITY;

-- Policies for access control
CREATE POLICY "Users can view their own milestones"
ON public.user_daily_milestones FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own milestones"
ON public.user_daily_milestones FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own milestones"
ON public.user_daily_milestones FOR UPDATE
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all milestones"
ON public.user_daily_milestones FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM public.user_roles
    WHERE user_roles.user_id = auth.uid() AND user_roles.role = 'admin'
  )
);

-- Function to update milestones when activities are added/updated
CREATE OR REPLACE FUNCTION public.update_daily_milestones()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    activity_user_id UUID;
    activity_date DATE;
    total_distance NUMERIC;
    milestone_km INTEGER;
    milestone_cashback NUMERIC;
BEGIN
    -- Get the user_id and date from the activity
    IF (TG_OP = 'DELETE') THEN
        activity_user_id := OLD.user_id;
        activity_date := OLD.activity_date;
    ELSE
        activity_user_id := NEW.user_id;
        activity_date := NEW.activity_date;
    END IF;

    -- Calculate total distance for the day
    SELECT COALESCE(SUM(distance_km), 0) INTO total_distance
    FROM public.user_activities
    WHERE user_id = activity_user_id AND activity_date = activity_date;

    -- Clear existing milestones for the day
    DELETE FROM public.user_daily_milestones
    WHERE user_id = activity_user_id AND activity_date = activity_date;

    -- Insert achieved milestones
    FOR milestone_km IN 1..LEAST(FLOOR(total_distance)::INTEGER, 10) LOOP
        -- Get cashback rate for this milestone
        milestone_cashback := public.get_cashback_base(milestone_km);
        
        INSERT INTO public.user_daily_milestones (
            user_id,
            activity_date,
            milestone_km,
            achieved_at,
            cashback_base
        ) VALUES (
            activity_user_id,
            activity_date,
            milestone_km,
            now(),
            milestone_cashback
        );
    END LOOP;

    RETURN NULL;
END;
$$;

-- Create trigger to update milestones when activities change
DROP TRIGGER IF EXISTS on_user_activity_update_milestones ON public.user_activities;
CREATE TRIGGER on_user_activity_update_milestones
AFTER INSERT OR UPDATE OR DELETE ON public.user_activities
FOR EACH ROW
EXECUTE FUNCTION public.update_daily_milestones();

-- Function to get milestone progress for a specific user and date
CREATE OR REPLACE FUNCTION public.get_user_milestone_progress(
    p_user_id UUID,
    p_date DATE DEFAULT CURRENT_DATE,
    p_max_milestones INTEGER DEFAULT 6
)
RETURNS TABLE(
    milestone_km INTEGER,
    achieved BOOLEAN,
    achieved_at TIMESTAMPTZ,
    cashback_base NUMERIC
)
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
    RETURN QUERY
    WITH milestone_range AS (
        SELECT generate_series(1, p_max_milestones) AS km
    )
    SELECT 
        mr.km,
        udm.achieved_at IS NOT NULL AS achieved,
        udm.achieved_at,
        COALESCE(udm.cashback_base, 0) AS cashback_base
    FROM milestone_range mr
    LEFT JOIN public.user_daily_milestones udm ON (
        udm.user_id = p_user_id 
        AND udm.activity_date = p_date 
        AND udm.milestone_km = mr.km
    )
    ORDER BY mr.km;
END;
$$;

-- Enhanced cashback calculation function that uses milestones
CREATE OR REPLACE FUNCTION public.calculate_milestone_cashback(
    p_user_id UUID,
    p_start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
    p_end_date DATE DEFAULT CURRENT_DATE
)
RETURNS NUMERIC
LANGUAGE plpgsql
STABLE
AS $$
DECLARE
    total_cashback_base NUMERIC := 0;
    days_in_period INTEGER;
BEGIN
    -- Calculate total cashback base from milestones
    SELECT COALESCE(SUM(cashback_base), 0) INTO total_cashback_base
    FROM public.user_daily_milestones
    WHERE user_id = p_user_id
      AND activity_date BETWEEN p_start_date AND p_end_date
      AND achieved_at IS NOT NULL;

    -- Calculate number of days in period
    days_in_period := (p_end_date - p_start_date + 1);

    -- Return percentage (cashback_base / days * 100)
    RETURN (total_cashback_base / days_in_period) * 100.0;
END;
$$;

-- Update existing user milestones for all existing activities
-- This ensures existing data is properly migrated
INSERT INTO public.user_daily_milestones (user_id, activity_date, milestone_km, achieved_at, cashback_base)
SELECT DISTINCT
    ua.user_id,
    ua.activity_date,
    milestone_km,
    ua.created_at,
    public.get_cashback_base(milestone_km)
FROM public.user_activities ua
CROSS JOIN generate_series(1, 10) AS milestone_km
WHERE milestone_km <= FLOOR(ua.distance_km)
ON CONFLICT (user_id, activity_date, milestone_km) DO NOTHING;

import { Target, CheckCircle2, Circle } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { calculateProgress, calculateTodayDistance } from './utils/formatters';
import { useMilestoneData, calculateMilestonesFromDistance, getCashbackRate } from './hooks/useMilestoneData';

interface TodayProgressCardProps {
  todayActivities: Array<{ distance_km: number }> | undefined;
  dailyGoal: number;
  isLoading: boolean;
}

export function TodayProgressCard({ todayActivities, dailyGoal, isLoading }: TodayProgressCardProps) {
  const todayDistance = calculateTodayDistance(todayActivities);
  const dailyProgress = calculateProgress(todayDistance, dailyGoal);

  // Use milestone data from database, fallback to calculated milestones
  const { milestoneProgress, totalCashback, isLoading: isLoadingMilestones } = useMilestoneData();

  // Fallback to calculated milestones if database data is not available
  const milestones = milestoneProgress.length > 0
    ? milestoneProgress.map(m => ({
        km: m.milestone_km,
        completed: m.achieved,
        progress: m.achieved ? 100 : Math.min(Math.max((todayDistance - (m.milestone_km - 1)) * 100, 0), 100),
        cashback_base: m.cashback_base
      }))
    : calculateMilestonesFromDistance(todayDistance, dailyGoal);

  // Calculate total potential cashback for completed milestones
  const completedMilestones = milestones.filter(m => m.completed);
  const totalCashbackBase = milestoneProgress.length > 0
    ? totalCashback
    : completedMilestones.reduce((sum, m) => sum + (m.cashback_base || getCashbackRate(m.km)), 0);

  return (
    <GlassCard className="mb-6 p-4 max-w-md mx-auto">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Target className="text-electric" size={20} />
          <span className="text-white/80 font-semibold">Today's Progress</span>
        </div>
        <span className="text-xs text-electric font-semibold">
          {isLoading || isLoadingMilestones ? '...' : `${todayDistance.toFixed(1)} / ${dailyGoal} km`}
        </span>
      </div>

      {/* Kilometer Milestones */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-xs text-white/60">Kilometer Milestones</span>
          <span className="text-xs text-electric">
            {isLoading || isLoadingMilestones ? '...' : `${completedMilestones.length}/${milestones.length} completed`}
          </span>
        </div>

        <div className="grid grid-cols-6 gap-2">
          {milestones.map((milestone) => (
            <div key={milestone.km} className="flex flex-col items-center">
              <div className="relative mb-1">
                {milestone.completed ? (
                  <CheckCircle2 className="text-electric w-6 h-6" />
                ) : (
                  <Circle className="text-white/30 w-6 h-6" />
                )}
                {!milestone.completed && milestone.progress > 0 && (
                  <div
                    className="absolute inset-0 rounded-full border-2 border-electric/50"
                    style={{
                      background: `conic-gradient(from 0deg, #00f5ff ${milestone.progress * 3.6}deg, transparent ${milestone.progress * 3.6}deg)`
                    }}
                  />
                )}
              </div>
              <span className={`text-xs ${milestone.completed ? 'text-electric' : 'text-white/40'}`}>
                {milestone.km}km
              </span>
              <span className={`text-xs ${milestone.completed ? 'text-green-400' : 'text-white/30'}`}>
                {milestone.completed ? `+${(milestone.cashback_base || getCashbackRate(milestone.km)).toFixed(2)}` : ''}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Overall Progress Bar */}
      <div className="w-full bg-white/10 rounded-full h-2 mb-2">
        <div
          className="bg-gradient-to-r from-electric to-purple h-2 rounded-full transition-all duration-300"
          style={{ width: `${isLoading ? 0 : dailyProgress}%` }}
        />
      </div>

      {/* Progress Summary */}
      <div className="flex justify-between text-xs text-white/60">
        <span>{isLoading ? '...' : `${dailyProgress.toFixed(0)}% complete`}</span>
        <span>{isLoading ? '...' : `${(dailyGoal - todayDistance).toFixed(1)} km to go`}</span>
      </div>

      {/* Cashback Summary */}
      {!isLoading && !isLoadingMilestones && completedMilestones.length > 0 && (
        <div className="mt-2 pt-2 border-t border-white/10">
          <div className="flex justify-between text-xs">
            <span className="text-white/60">Today's Cashback Base:</span>
            <span className="text-electric font-semibold">+{totalCashbackBase.toFixed(2)}</span>
          </div>
        </div>
      )}
    </GlassCard>
  );
}

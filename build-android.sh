#!/bin/bash

echo "========================================"
echo "SoloGrind Android Build Script"
echo "========================================"
echo

echo "Checking prerequisites..."

# Check if Java is installed
if ! command -v java &> /dev/null; then
    echo "ERROR: Java is not installed or not in PATH"
    echo "Please install JDK 17 and set JAVA_HOME"
    echo "See ANDROID_BUILD_GUIDE.md for instructions"
    exit 1
fi

echo "✓ Java is installed"

# Check if Android SDK is available
if [ -z "$ANDROID_HOME" ]; then
    echo "WARNING: ANDROID_HOME is not set"
    echo "Please set ANDROID_HOME to your Android SDK location"
    echo "See ANDROID_BUILD_GUIDE.md for instructions"
fi

echo
echo "Building production web app..."
npm run build:prod
if [ $? -ne 0 ]; then
    echo "ERROR: Web app build failed"
    exit 1
fi

echo "✓ Web app built successfully"

echo
echo "Syncing to Android..."
npx cap sync android
if [ $? -ne 0 ]; then
    echo "ERROR: Capacitor sync failed"
    exit 1
fi

echo "✓ Synced to Android successfully"

echo
echo "Attempting to build Android APK..."
cd android
./gradlew assembleDebug
if [ $? -ne 0 ]; then
    echo
    echo "ERROR: Android build failed"
    echo "This is likely due to missing Android SDK or Java configuration"
    echo
    echo "Please follow these steps:"
    echo "1. Install Android Studio and Android SDK"
    echo "2. Set ANDROID_HOME environment variable"
    echo "3. Set JAVA_HOME environment variable"
    echo "4. See ANDROID_BUILD_GUIDE.md for detailed instructions"
    echo
    echo "Alternatively, you can open the project in Android Studio:"
    echo "  npx cap open android"
    echo
    cd ..
    exit 1
fi

cd ..

echo
echo "========================================"
echo "✓ BUILD SUCCESSFUL!"
echo "========================================"
echo
echo "APK Location: android/app/build/outputs/apk/debug/app-debug.apk"
echo
echo "To install on your device:"
echo "1. Enable USB Debugging on your Android device"
echo "2. Connect via USB"
echo "3. Run: adb install android/app/build/outputs/apk/debug/app-debug.apk"
echo
echo "Or copy the APK file to your device and install directly"
echo

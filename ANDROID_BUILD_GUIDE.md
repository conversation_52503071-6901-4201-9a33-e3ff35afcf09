# SoloGrind Android Build & Installation Guide

## Prerequisites Setup

### 1. Install Java Development Kit (JDK)
- Download and install **JDK 17** from [Oracle](https://www.oracle.com/java/technologies/javase/jdk17-archive-downloads.html) or [OpenJDK](https://adoptium.net/)
- Set JAVA_HOME environment variable:
  - Windows: Add `JAVA_HOME=C:\Program Files\Java\jdk-17` to system environment variables
  - Add `%JAVA_HOME%\bin` to your PATH

### 2. Install Android Studio
- Download [Android Studio](https://developer.android.com/studio)
- During installation, make sure to install:
  - Android SDK
  - Android SDK Platform-Tools
  - Android Virtual Device (AVD)

### 3. Configure Android SDK
- Open Android Studio
- Go to File → Settings → Appearance & Behavior → System Settings → Android SDK
- Install the following:
  - Android SDK Platform 34 (API Level 34)
  - Android SDK Build-Tools 34.0.0
  - Android SDK Command-line Tools
- Set ANDROID_HOME environment variable:
  - Windows: Add `ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk`
  - Add `%ANDROID_HOME%\platform-tools` and `%ANDROID_HOME%\tools` to your PATH

## Building the APK

### Option 1: Using Capacitor CLI (Recommended)
```bash
# Build the web app and sync to Android
npm run cap:build

# Open in Android Studio to build APK
npx cap open android
```

### Option 2: Command Line Build
```bash
# Navigate to android directory
cd android

# Build debug APK
./gradlew assembleDebug

# Build release APK (for production)
./gradlew assembleRelease
```

### Option 3: Using npm scripts
```bash
# Build and run on connected device/emulator
npm run android:build

# Build for release
npm run android:release
```

## APK Location
After successful build, the APK files will be located at:
- **Debug APK**: `android/app/build/outputs/apk/debug/app-debug.apk`
- **Release APK**: `android/app/build/outputs/apk/release/app-release.apk`

## Installing APK on Android Device

### Method 1: USB Installation (ADB)
1. Enable Developer Options on your Android device:
   - Go to Settings → About Phone
   - Tap "Build Number" 7 times
   - Go back to Settings → Developer Options
   - Enable "USB Debugging"

2. Connect your device via USB and install:
```bash
# Install debug APK
adb install android/app/build/outputs/apk/debug/app-debug.apk

# Or install release APK
adb install android/app/build/outputs/apk/release/app-release.apk
```

### Method 2: Direct Installation
1. Copy the APK file to your Android device
2. Open the APK file on your device
3. Allow installation from unknown sources if prompted
4. Follow the installation prompts

### Method 3: Using Android Studio
1. Open the project in Android Studio: `npx cap open android`
2. Connect your device or start an emulator
3. Click the "Run" button (green play icon)

## Testing the App

### Features to Test:
1. **Authentication**: Login/Register functionality
2. **Navigation**: All app screens and routing
3. **Chat System**: Global and guild chat functionality
4. **Fitness Tracking**: Workout logging and progress tracking
5. **Payment Integration**: Wallet and payment features
6. **Offline Functionality**: App behavior without internet
7. **Performance**: App responsiveness and loading times

### Mobile-Specific Testing:
- Touch interactions and gestures
- Screen orientation changes
- App lifecycle (background/foreground)
- Push notifications (if implemented)
- Camera and file access permissions
- Network connectivity changes

## Troubleshooting

### Common Issues:

1. **Java/Android SDK not found**:
   - Verify JAVA_HOME and ANDROID_HOME environment variables
   - Restart your terminal/IDE after setting environment variables

2. **Build fails with dependency errors**:
   ```bash
   cd android
   ./gradlew clean
   ./gradlew assembleDebug
   ```

3. **App crashes on startup**:
   - Check device logs: `adb logcat`
   - Ensure all required permissions are granted

4. **Network requests fail**:
   - Check if your Supabase configuration is correct
   - Verify network permissions in AndroidManifest.xml

## Development Workflow

### For Live Development:
```bash
# Start development server with live reload
npm run android:dev
```

### For Production Testing:
```bash
# Build optimized version
npm run android:release
```

## App Information
- **App Name**: SoloGrind
- **Package ID**: com.sologrind.app
- **Version**: 1.0
- **Minimum Android Version**: API Level 24 (Android 7.0)
- **Target Android Version**: API Level 34 (Android 14)

## Next Steps
1. Install the required development tools
2. Build the APK using one of the methods above
3. Install on your Android device
4. Test all app functionality
5. Report any issues or bugs found during testing

For any issues during the build process, check the Android Studio logs or run the build commands in verbose mode for detailed error information.

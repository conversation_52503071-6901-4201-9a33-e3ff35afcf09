import { test, expect } from '@playwright/test';

test.describe('Milestone Tracking Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the Track Your Run page
    await page.goto('http://localhost:8080');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Navigate to Track Your Run page if not already there
    const trackRunButton = page.locator('text=Track Your Run').first();
    if (await trackRunButton.isVisible()) {
      await trackRunButton.click();
      await page.waitForLoadState('networkidle');
    }
  });

  test('should display kilometer milestones in progress card', async ({ page }) => {
    // Check if the Today's Progress card is visible
    const progressCard = page.locator('[data-testid="today-progress-card"]').first();
    await expect(progressCard.or(page.locator('text=Today\'s Progress').locator('..'))).toBeVisible();

    // Check if milestone section is visible
    const milestoneSection = page.locator('text=Kilometer Milestones').first();
    await expect(milestoneSection).toBeVisible();

    // Check if individual kilometer markers are displayed (1km to 6km)
    for (let i = 1; i <= 6; i++) {
      const kmMarker = page.locator(`text=${i}km`).first();
      await expect(kmMarker).toBeVisible();
    }
  });

  test('should show milestone completion status', async ({ page }) => {
    // Look for milestone completion indicators
    const completedText = page.locator('text=/\\d+\\/\\d+ completed/').first();
    await expect(completedText).toBeVisible();

    // Check for milestone icons (circles or check marks)
    const milestoneIcons = page.locator('svg').filter({ hasText: /^$/ });
    await expect(milestoneIcons.first()).toBeVisible();
  });

  test('should display cashback information for completed milestones', async ({ page }) => {
    // Look for cashback base information
    const cashbackSection = page.locator('text=Today\'s Cashback Base').first();
    
    // If there are completed milestones, cashback should be visible
    const completedMilestones = await page.locator('text=/\\d+\\/\\d+ completed/').first().textContent();
    if (completedMilestones && completedMilestones.startsWith('1') || completedMilestones.startsWith('2') || completedMilestones.startsWith('3') || completedMilestones.startsWith('4') || completedMilestones.startsWith('5') || completedMilestones.startsWith('6')) {
      await expect(cashbackSection).toBeVisible();
    }
  });

  test('should show overall progress bar', async ({ page }) => {
    // Check if the main progress bar is visible
    const progressBar = page.locator('.bg-gradient-to-r').first();
    await expect(progressBar).toBeVisible();

    // Check if progress percentage is displayed
    const progressText = page.locator('text=/\\d+% complete/').first();
    await expect(progressText).toBeVisible();
  });

  test('should display distance information', async ({ page }) => {
    // Check if current distance vs goal is shown
    const distanceInfo = page.locator('text=/\\d+\\.\\d+ \\/ \\d+ km/').first();
    await expect(distanceInfo).toBeVisible();

    // Check if "km to go" information is shown
    const toGoInfo = page.locator('text=/\\d+\\.\\d+ km to go/').first();
    await expect(toGoInfo).toBeVisible();
  });

  test('should handle loading states properly', async ({ page }) => {
    // Reload the page to test loading states
    await page.reload();
    
    // Check if loading indicators are shown initially
    const loadingIndicators = page.locator('text=...').first();
    
    // Wait for content to load
    await page.waitForLoadState('networkidle');
    
    // Verify that actual content replaces loading indicators
    const progressCard = page.locator('text=Today\'s Progress').locator('..').first();
    await expect(progressCard).toBeVisible();
  });

  test('milestone visual indicators should be responsive', async ({ page }) => {
    // Test different viewport sizes
    await page.setViewportSize({ width: 375, height: 667 }); // Mobile
    
    const milestoneSection = page.locator('text=Kilometer Milestones').first();
    await expect(milestoneSection).toBeVisible();
    
    // Check if milestone grid is still visible on mobile
    const milestoneGrid = page.locator('.grid-cols-6').first();
    await expect(milestoneGrid).toBeVisible();
    
    // Test tablet size
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(milestoneSection).toBeVisible();
    
    // Test desktop size
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(milestoneSection).toBeVisible();
  });
});

test.describe('Milestone Tracking Integration', () => {
  test('should update milestones when workout is completed', async ({ page }) => {
    // This test would require a more complex setup with mock data
    // For now, we'll just verify the UI components are in place
    
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
    
    // Navigate to Track Your Run
    const trackRunButton = page.locator('text=Track Your Run').first();
    if (await trackRunButton.isVisible()) {
      await trackRunButton.click();
      await page.waitForLoadState('networkidle');
    }
    
    // Verify tracking controls are present
    const startButton = page.locator('button').filter({ hasText: /start/i }).first();
    await expect(startButton.or(page.locator('[data-testid="start-tracking"]'))).toBeVisible();
    
    // Verify milestone display is present
    const milestoneSection = page.locator('text=Kilometer Milestones').first();
    await expect(milestoneSection).toBeVisible();
  });
});

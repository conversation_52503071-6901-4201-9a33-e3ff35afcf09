{"config": {"configFile": "C:\\Users\\<USER>\\Desktop\\Solo Grind\\playwright.config.js", "rootDir": "C:/Users/<USER>/Desktop/Solo Grind/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 2}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Desktop/Solo Grind/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "chromium-desktop", "name": "chromium-desktop", "testDir": "C:/Users/<USER>/Desktop/Solo Grind/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/Solo Grind/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "firefox-desktop", "name": "firefox-desktop", "testDir": "C:/Users/<USER>/Desktop/Solo Grind/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/Solo Grind/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "webkit-desktop", "name": "webkit-desktop", "testDir": "C:/Users/<USER>/Desktop/Solo Grind/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/Solo Grind/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "mobile-chrome", "name": "mobile-chrome", "testDir": "C:/Users/<USER>/Desktop/Solo Grind/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/Solo Grind/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "mobile-safari", "name": "mobile-safari", "testDir": "C:/Users/<USER>/Desktop/Solo Grind/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/Solo Grind/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "tablet-chrome", "name": "tablet-chrome", "testDir": "C:/Users/<USER>/Desktop/Solo Grind/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.2", "workers": 2, "webServer": {"command": "npm run dev", "url": "http://localhost:8080", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "milestone-tracking.test.js", "file": "milestone-tracking.test.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Milestone Tracking Functionality", "file": "milestone-tracking.test.js", "line": 3, "column": 6, "specs": [{"title": "should display kilometer milestones in progress card", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium-desktop", "projectName": "chromium-desktop", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 6, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-06T21:41:38.356Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "3fefe61bde23b4bfac4c-ab92aa420073ea623ea0", "file": "milestone-tracking.test.js", "line": 19, "column": 3}, {"title": "should show milestone completion status", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium-desktop", "projectName": "chromium-desktop", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 7, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-06T21:41:38.356Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "3fefe61bde23b4bfac4c-9f20f1c2984190d70d46", "file": "milestone-tracking.test.js", "line": 35, "column": 3}, {"title": "should display cashback information for completed milestones", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium-desktop", "projectName": "chromium-desktop", "results": [{"workerIndex": 2, "parallelIndex": 1, "status": "failed", "duration": 9, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-06T21:41:40.492Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "3fefe61bde23b4bfac4c-4c47936a228247f89ad4", "file": "milestone-tracking.test.js", "line": 45, "column": 3}, {"title": "should show overall progress bar", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium-desktop", "projectName": "chromium-desktop", "results": [{"workerIndex": 3, "parallelIndex": 0, "status": "failed", "duration": 6, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-06T21:41:40.483Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "3fefe61bde23b4bfac4c-6fc914d002b031e43d04", "file": "milestone-tracking.test.js", "line": 56, "column": 3}, {"title": "should display distance information", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium-desktop", "projectName": "chromium-desktop", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "failed", "duration": 10, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-06T21:41:42.598Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "3fefe61bde23b4bfac4c-b21d006ac9124ea1cb30", "file": "milestone-tracking.test.js", "line": 66, "column": 3}, {"title": "should handle loading states properly", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium-desktop", "projectName": "chromium-desktop", "results": [{"workerIndex": 5, "parallelIndex": 1, "status": "failed", "duration": 17, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-06T21:41:42.622Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "3fefe61bde23b4bfac4c-6d60f6c6054490a79afa", "file": "milestone-tracking.test.js", "line": 76, "column": 3}, {"title": "milestone visual indicators should be responsive", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium-desktop", "projectName": "chromium-desktop", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "failed", "duration": 11, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-06T21:41:44.880Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "3fefe61bde23b4bfac4c-66ee4dd2bc0561a50eeb", "file": "milestone-tracking.test.js", "line": 91, "column": 3}]}, {"title": "Milestone Tracking Integration", "file": "milestone-tracking.test.js", "line": 112, "column": 6, "specs": [{"title": "should update milestones when workout is completed", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium-desktop", "projectName": "chromium-desktop", "results": [{"workerIndex": 7, "parallelIndex": 1, "status": "failed", "duration": 14, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1179\\chrome-win\\headless_shell.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-06T21:41:44.929Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "3fefe61bde23b4bfac4c-21abe5cfc116f11beeef", "file": "milestone-tracking.test.js", "line": 113, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-06T21:41:36.254Z", "duration": 8775.128, "expected": 0, "skipped": 0, "unexpected": 8, "flaky": 0}}
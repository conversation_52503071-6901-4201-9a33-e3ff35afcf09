// Test script to verify milestone functionality
import { createClient } from '@supabase/supabase-js';

// Note: In a real environment, these would be environment variables
const supabaseUrl = 'https://xutzuilymxmhcqhnohwq.supabase.co';
const supabaseKey = 'your-anon-key-here'; // Replace with actual key

const supabase = createClient(supabaseUrl, supabaseKey);

async function testMilestoneFunctionality() {
  console.log('Testing milestone functionality...');

  try {
    // Test 1: Check if milestone functions exist
    console.log('\n1. Testing milestone progress function...');
    const { data: milestoneData, error: milestoneError } = await supabase.rpc('get_user_milestone_progress', {
      p_user_id: '00000000-0000-0000-0000-000000000000', // Test UUID
      p_date: '2025-01-07',
      p_max_milestones: 6
    });

    if (milestoneError) {
      console.error('Milestone function error:', milestoneError);
    } else {
      console.log('Milestone function works! Sample data:', milestoneData);
    }

    // Test 2: Check cashback calculation function
    console.log('\n2. Testing cashback calculation function...');
    const { data: cashbackData, error: cashbackError } = await supabase.rpc('calculate_milestone_cashback', {
      p_user_id: '00000000-0000-0000-0000-000000000000', // Test UUID
      p_start_date: '2025-01-07',
      p_end_date: '2025-01-07'
    });

    if (cashbackError) {
      console.error('Cashback function error:', cashbackError);
    } else {
      console.log('Cashback function works! Result:', cashbackData);
    }

    // Test 3: Check if milestone table exists
    console.log('\n3. Testing milestone table access...');
    const { data: tableData, error: tableError } = await supabase
      .from('user_daily_milestones')
      .select('*')
      .limit(1);

    if (tableError) {
      console.error('Table access error:', tableError);
    } else {
      console.log('Milestone table accessible! Sample:', tableData);
    }

    console.log('\n✅ Milestone functionality test completed!');

  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
testMilestoneFunctionality();
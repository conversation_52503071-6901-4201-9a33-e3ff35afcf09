<testsuites id="" name="" tests="8" failures="8" skipped="0" errors="0" time="8.775128">
<testsuite name="milestone-tracking.test.js" timestamp="2025-07-06T21:41:36.399Z" hostname="chromium-desktop" tests="8" failures="8" skipped="0" time="0.08" errors="0">
<testcase name="Milestone Tracking Functionality › should display kilometer milestones in progress card" classname="milestone-tracking.test.js" time="0.006">
<failure message="milestone-tracking.test.js:19:3 should display kilometer milestones in progress card" type="FAILURE">
<![CDATA[  [chromium-desktop] › milestone-tracking.test.js:19:3 › Milestone Tracking Functionality › should display kilometer milestones in progress card 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\chromium_headless_shell-1179\chrome-win\headless_shell.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Milestone Tracking Functionality › should show milestone completion status" classname="milestone-tracking.test.js" time="0.007">
<failure message="milestone-tracking.test.js:35:3 should show milestone completion status" type="FAILURE">
<![CDATA[  [chromium-desktop] › milestone-tracking.test.js:35:3 › Milestone Tracking Functionality › should show milestone completion status 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\chromium_headless_shell-1179\chrome-win\headless_shell.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Milestone Tracking Functionality › should display cashback information for completed milestones" classname="milestone-tracking.test.js" time="0.009">
<failure message="milestone-tracking.test.js:45:3 should display cashback information for completed milestones" type="FAILURE">
<![CDATA[  [chromium-desktop] › milestone-tracking.test.js:45:3 › Milestone Tracking Functionality › should display cashback information for completed milestones 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\chromium_headless_shell-1179\chrome-win\headless_shell.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Milestone Tracking Functionality › should show overall progress bar" classname="milestone-tracking.test.js" time="0.006">
<failure message="milestone-tracking.test.js:56:3 should show overall progress bar" type="FAILURE">
<![CDATA[  [chromium-desktop] › milestone-tracking.test.js:56:3 › Milestone Tracking Functionality › should show overall progress bar 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\chromium_headless_shell-1179\chrome-win\headless_shell.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Milestone Tracking Functionality › should display distance information" classname="milestone-tracking.test.js" time="0.01">
<failure message="milestone-tracking.test.js:66:3 should display distance information" type="FAILURE">
<![CDATA[  [chromium-desktop] › milestone-tracking.test.js:66:3 › Milestone Tracking Functionality › should display distance information 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\chromium_headless_shell-1179\chrome-win\headless_shell.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Milestone Tracking Functionality › should handle loading states properly" classname="milestone-tracking.test.js" time="0.017">
<failure message="milestone-tracking.test.js:76:3 should handle loading states properly" type="FAILURE">
<![CDATA[  [chromium-desktop] › milestone-tracking.test.js:76:3 › Milestone Tracking Functionality › should handle loading states properly 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\chromium_headless_shell-1179\chrome-win\headless_shell.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Milestone Tracking Functionality › milestone visual indicators should be responsive" classname="milestone-tracking.test.js" time="0.011">
<failure message="milestone-tracking.test.js:91:3 milestone visual indicators should be responsive" type="FAILURE">
<![CDATA[  [chromium-desktop] › milestone-tracking.test.js:91:3 › Milestone Tracking Functionality › milestone visual indicators should be responsive 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\chromium_headless_shell-1179\chrome-win\headless_shell.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Milestone Tracking Integration › should update milestones when workout is completed" classname="milestone-tracking.test.js" time="0.014">
<failure message="milestone-tracking.test.js:113:3 should update milestones when workout is completed" type="FAILURE">
<![CDATA[  [chromium-desktop] › milestone-tracking.test.js:113:3 › Milestone Tracking Integration › should update milestones when workout is completed 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\chromium_headless_shell-1179\chrome-win\headless_shell.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
</testsuite>
</testsuites>